/// <reference types="vitest" />
import { UserConfig } from "vite";
import path from "node:path";
import vue from "@vitejs/plugin-vue";

export default <UserConfig>{
   plugins: [vue()],
   resolve: {
      alias: {
         "@": path.resolve(__dirname, "."),
      },
   },
   server: {
      proxy: {
         "/api": {
            target: "http://localhost:8000",
            changeOrigin: true,
         },
      },
   },
   test: {
      environment: "happy-dom",
   },
};
