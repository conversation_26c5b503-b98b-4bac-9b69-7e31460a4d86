import { <PERSON><PERSON> } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Switch } from '@/Components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/Components/ui/tabs';
import { Textarea } from '@/Components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/Components/ui/tooltip';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, useForm, usePage } from '@inertiajs/react';
import {
    AlertCircle,
    BarChart3,
    Code,
    FileText,
    Globe,
    HelpCircle,
    Info,
    Save,
    Search,
    Settings,
    Share2,
    Shield,
} from 'lucide-react';
import { FormEventHandler, useEffect, useState } from 'react';
import axios from 'axios';

type HtmlPreview = {
    success: boolean;
    html?: string;
    last_modified?: number;
    last_modified_human?: string;
    preview?: {
        title?: string;
        description?: string;
        og_title?: string;
        twitter_card?: string;
    };
    message?: string;
};

type SeoSettingsForm = {
    // Basic Meta Tags
    site_title: string;
    title_separator: string;
    meta_description: string;
    meta_keywords: string;
    canonical_url: string;
    // Open Graph
    og_site_name: string;
    og_title: string;
    og_description: string;
    og_image: string;
    og_image_width: number | null;
    og_image_height: number | null;
    og_type: string;
    og_locale: string;
    // Twitter Cards
    twitter_card_type: string;
    twitter_site: string;
    twitter_creator: string;
    twitter_title: string;
    twitter_description: string;
    twitter_image: string;
    // Robots & Indexing
    robots_index: boolean;
    robots_follow: boolean;
    robots_noarchive: boolean;
    robots_nosnippet: boolean;
    robots_max_snippet: number | null;
    robots_max_image_preview: string;
    robots_max_video_preview: number | null;
    googlebot_specific: string;
    bingbot_specific: string;
    // Schema.org Organization
    schema_organization_enabled: boolean;
    schema_organization_name: string;
    schema_organization_logo: string;
    schema_organization_url: string;
    schema_organization_contact_type: string;
    schema_organization_phone: string;
    schema_organization_email: string;
    schema_organization_address: string;
    schema_organization_social_profiles: string[];
    // Schema.org Website
    schema_website_enabled: boolean;
    schema_website_name: string;
    schema_website_alternate_name: string;
    schema_search_enabled: boolean;
    schema_search_url_template: string;
    // Schema.org Local Business
    schema_local_business_enabled: boolean;
    schema_local_business_type: string;
    schema_local_business_name: string;
    schema_local_business_image: string;
    schema_local_business_price_range: string;
    schema_local_business_opening_hours: string[];
    schema_local_business_geo_lat: number | null;
    schema_local_business_geo_lng: number | null;
    // Verification & Analytics
    google_site_verification: string;
    bing_site_verification: string;
    yandex_verification: string;
    pinterest_verification: string;
    facebook_domain_verification: string;
    google_analytics_id: string;
    google_tag_manager_id: string;
    facebook_pixel_id: string;
    tiktok_pixel_id: string;
    // Advanced Settings
    custom_head_scripts: string;
    custom_body_start_scripts: string;
    custom_body_end_scripts: string;
    default_og_image_fallback: string;
    auto_generate_description: boolean;
    description_max_length: number;
    // Sitemap Settings
    sitemap_enabled: boolean;
    sitemap_include_images: boolean;
    sitemap_include_lastmod: boolean;
    sitemap_changefreq_default: string;
    sitemap_priority_default: number;
    // Robots.txt Settings
    robots_txt_content: string;
    robots_txt_sitemap_url: string;
};

interface SeoSettingsData extends SeoSettingsForm {
    id?: number;
}

interface Props {
    seoSettings: SeoSettingsData;
}

function FieldTooltip({ content }: { content: string }) {
    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 inline h-4 w-4 cursor-help text-gray-400" />
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                    <p className="text-sm">{content}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}

export default function SeoSettings({ seoSettings }: Props) {
    const user = usePage().props.auth.user as { current_organization_id: number };
    const [activeTab, setActiveTab] = useState('general');
    const [socialProfiles, setSocialProfiles] = useState<string[]>(
        seoSettings.schema_organization_social_profiles || [''],
    );
    const [htmlPreview, setHtmlPreview] = useState<HtmlPreview | null>(null);
    const [loadingPreview, setLoadingPreview] = useState(false);
    const [regenerating, setRegenerating] = useState(false);

    const { data, setData, put, processing, errors, isDirty, reset } = useForm<SeoSettingsForm>({
        // Basic Meta Tags
        site_title: seoSettings.site_title || '',
        title_separator: seoSettings.title_separator || '|',
        meta_description: seoSettings.meta_description || '',
        meta_keywords: seoSettings.meta_keywords || '',
        canonical_url: seoSettings.canonical_url || '',
        // Open Graph
        og_site_name: seoSettings.og_site_name || '',
        og_title: seoSettings.og_title || '',
        og_description: seoSettings.og_description || '',
        og_image: seoSettings.og_image || '',
        og_image_width: seoSettings.og_image_width || null,
        og_image_height: seoSettings.og_image_height || null,
        og_type: seoSettings.og_type || 'website',
        og_locale: seoSettings.og_locale || 'vi_VN',
        // Twitter Cards
        twitter_card_type: seoSettings.twitter_card_type || 'summary_large_image',
        twitter_site: seoSettings.twitter_site || '',
        twitter_creator: seoSettings.twitter_creator || '',
        twitter_title: seoSettings.twitter_title || '',
        twitter_description: seoSettings.twitter_description || '',
        twitter_image: seoSettings.twitter_image || '',
        // Robots & Indexing
        robots_index: seoSettings.robots_index ?? true,
        robots_follow: seoSettings.robots_follow ?? true,
        robots_noarchive: seoSettings.robots_noarchive ?? false,
        robots_nosnippet: seoSettings.robots_nosnippet ?? false,
        robots_max_snippet: seoSettings.robots_max_snippet || null,
        robots_max_image_preview: seoSettings.robots_max_image_preview || 'large',
        robots_max_video_preview: seoSettings.robots_max_video_preview || null,
        googlebot_specific: seoSettings.googlebot_specific || '',
        bingbot_specific: seoSettings.bingbot_specific || '',
        // Schema.org Organization
        schema_organization_enabled: seoSettings.schema_organization_enabled ?? false,
        schema_organization_name: seoSettings.schema_organization_name || '',
        schema_organization_logo: seoSettings.schema_organization_logo || '',
        schema_organization_url: seoSettings.schema_organization_url || '',
        schema_organization_contact_type: seoSettings.schema_organization_contact_type || '',
        schema_organization_phone: seoSettings.schema_organization_phone || '',
        schema_organization_email: seoSettings.schema_organization_email || '',
        schema_organization_address: seoSettings.schema_organization_address || '',
        schema_organization_social_profiles: seoSettings.schema_organization_social_profiles || [],
        // Schema.org Website
        schema_website_enabled: seoSettings.schema_website_enabled ?? false,
        schema_website_name: seoSettings.schema_website_name || '',
        schema_website_alternate_name: seoSettings.schema_website_alternate_name || '',
        schema_search_enabled: seoSettings.schema_search_enabled ?? false,
        schema_search_url_template: seoSettings.schema_search_url_template || '',
        // Schema.org Local Business
        schema_local_business_enabled: seoSettings.schema_local_business_enabled ?? false,
        schema_local_business_type: seoSettings.schema_local_business_type || 'LocalBusiness',
        schema_local_business_name: seoSettings.schema_local_business_name || '',
        schema_local_business_image: seoSettings.schema_local_business_image || '',
        schema_local_business_price_range: seoSettings.schema_local_business_price_range || '',
        schema_local_business_opening_hours: seoSettings.schema_local_business_opening_hours || [],
        schema_local_business_geo_lat: seoSettings.schema_local_business_geo_lat || null,
        schema_local_business_geo_lng: seoSettings.schema_local_business_geo_lng || null,
        // Verification & Analytics
        google_site_verification: seoSettings.google_site_verification || '',
        bing_site_verification: seoSettings.bing_site_verification || '',
        yandex_verification: seoSettings.yandex_verification || '',
        pinterest_verification: seoSettings.pinterest_verification || '',
        facebook_domain_verification: seoSettings.facebook_domain_verification || '',
        google_analytics_id: seoSettings.google_analytics_id || '',
        google_tag_manager_id: seoSettings.google_tag_manager_id || '',
        facebook_pixel_id: seoSettings.facebook_pixel_id || '',
        tiktok_pixel_id: seoSettings.tiktok_pixel_id || '',
        // Advanced Settings
        custom_head_scripts: seoSettings.custom_head_scripts || '',
        custom_body_start_scripts: seoSettings.custom_body_start_scripts || '',
        custom_body_end_scripts: seoSettings.custom_body_end_scripts || '',
        default_og_image_fallback: seoSettings.default_og_image_fallback || '',
        auto_generate_description: seoSettings.auto_generate_description ?? false,
        description_max_length: seoSettings.description_max_length || 160,
        // Sitemap Settings
        sitemap_enabled: seoSettings.sitemap_enabled ?? true,
        sitemap_include_images: seoSettings.sitemap_include_images ?? true,
        sitemap_include_lastmod: seoSettings.sitemap_include_lastmod ?? true,
        sitemap_changefreq_default: seoSettings.sitemap_changefreq_default || 'weekly',
        sitemap_priority_default: seoSettings.sitemap_priority_default || 0.5,
        // Robots.txt Settings
        robots_txt_content: seoSettings.robots_txt_content || '',
        robots_txt_sitemap_url: seoSettings.robots_txt_sitemap_url || '',
    });

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        put(route('organizations.seo-settings.update', { organization: user.current_organization_id }), {
            preserveScroll: true,
            onSuccess: () => {
                // Refresh HTML preview after successful save
                fetchHtmlPreview();
            },
        });
    };

    const addSocialProfile = () => {
        setSocialProfiles([...socialProfiles, '']);
    };

    const updateSocialProfile = (index: number, value: string) => {
        const newProfiles = [...socialProfiles];
        newProfiles[index] = value;
        setSocialProfiles(newProfiles);
        setData('schema_organization_social_profiles', newProfiles.filter((p) => p.trim() !== ''));
    };

    const removeSocialProfile = (index: number) => {
        const newProfiles = socialProfiles.filter((_, i) => i !== index);
        setSocialProfiles(newProfiles.length > 0 ? newProfiles : ['']);
        setData('schema_organization_social_profiles', newProfiles.filter((p) => p.trim() !== ''));
    };

    const fetchHtmlPreview = async () => {
        setLoadingPreview(true);
        try {
            const response = await axios.get(
                route('organizations.seo-settings.html-preview', {
                    organization: user.current_organization_id,
                }),
            );
            setHtmlPreview(response.data);
        } catch (error) {
            console.error('Failed to fetch HTML preview:', error);
            setHtmlPreview({
                success: false,
                message: 'Failed to load HTML preview',
            });
        } finally {
            setLoadingPreview(false);
        }
    };

    const handleRegenerate = async () => {
        setRegenerating(true);
        try {
            await axios.post(
                route('organizations.seo-settings.regenerate', {
                    organization: user.current_organization_id,
                }),
            );
            // Refresh preview after regeneration
            await fetchHtmlPreview();
        } catch (error) {
            console.error('Failed to regenerate HTML:', error);
        } finally {
            setRegenerating(false);
        }
    };

    // Load preview on mount
    useEffect(() => {
        fetchHtmlPreview();
    }, []);

    // Sync social profiles state when form is reset
    useEffect(() => {
        const formProfiles = data.schema_organization_social_profiles;
        const currentProfiles = socialProfiles.filter((p) => p.trim() !== '');

        // Only update if form data differs from local state (happens on reset)
        if (JSON.stringify(formProfiles) !== JSON.stringify(currentProfiles)) {
            setSocialProfiles(formProfiles.length > 0 ? formProfiles : ['']);
        }
    }, [data.schema_organization_social_profiles]);

    return (
        <AuthenticatedLayout
            breadcrumbs={[{ title: 'Organization' }, { title: 'SEO Settings' }]}
        >
            <Head title="SEO Settings" />

            <div className="mx-4 mb-4">
                <div className="mb-6 flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">SEO Settings</h1>
                        <p className="text-sm text-gray-500">
                            Configure search engine optimization and social media sharing settings
                        </p>
                    </div>
                    <div className="flex gap-2">
                        {isDirty && (
                            <Button type="button" variant="outline" onClick={() => reset()}>
                                Reset
                            </Button>
                        )}
                        <Button onClick={handleSubmit} disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Saving...' : 'Save Settings'}
                        </Button>
                    </div>
                </div>

                {/* HTML Preview Section */}
                <Card className="mb-6 border-blue-200 bg-blue-50">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    <Info className="h-5 w-5 text-blue-600" />
                                    Generated Frontend HTML Preview
                                </CardTitle>
                                <CardDescription>
                                    Current SEO meta tags injected into website-frontend/index.html
                                </CardDescription>
                            </div>
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={handleRegenerate}
                                disabled={regenerating}
                            >
                                {regenerating ? 'Regenerating...' : 'Regenerate HTML'}
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {loadingPreview ? (
                            <p className="text-sm text-gray-500">Loading preview...</p>
                        ) : htmlPreview?.success ? (
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium text-gray-700">Title:</p>
                                        <p className="text-sm text-gray-900">
                                            {htmlPreview.preview?.title || 'No title found'}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium text-gray-700">Last Modified:</p>
                                        <p className="text-sm text-gray-900">
                                            {htmlPreview.last_modified_human || 'Unknown'}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium text-gray-700">Description:</p>
                                        <p className="text-sm text-gray-900">
                                            {htmlPreview.preview?.description || 'No description found'}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium text-gray-700">OG Title:</p>
                                        <p className="text-sm text-gray-900">
                                            {htmlPreview.preview?.og_title || 'No OG title found'}
                                        </p>
                                    </div>
                                </div>
                                <details className="mt-4">
                                    <summary className="cursor-pointer text-sm font-medium text-blue-700 hover:text-blue-800">
                                        View Full HTML Source
                                    </summary>
                                    <pre className="mt-2 max-h-64 overflow-auto rounded-md bg-gray-900 p-4 text-xs text-green-400">
                                        {htmlPreview.html}
                                    </pre>
                                </details>
                            </div>
                        ) : (
                            <div className="flex items-center gap-2 text-amber-700">
                                <AlertCircle className="h-5 w-5" />
                                <p className="text-sm">{htmlPreview?.message || 'Failed to load preview'}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                <form onSubmit={handleSubmit}>
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                        <TabsList className="mb-6 flex-wrap">
                            <TabsTrigger value="general" className="gap-2">
                                <Settings className="h-4 w-4" />
                                General
                            </TabsTrigger>
                            <TabsTrigger value="social" className="gap-2">
                                <Share2 className="h-4 w-4" />
                                Social Media
                            </TabsTrigger>
                            <TabsTrigger value="indexing" className="gap-2">
                                <Search className="h-4 w-4" />
                                Indexing
                            </TabsTrigger>
                            <TabsTrigger value="schema" className="gap-2">
                                <Code className="h-4 w-4" />
                                Schema Markup
                            </TabsTrigger>
                            <TabsTrigger value="integrations" className="gap-2">
                                <BarChart3 className="h-4 w-4" />
                                Integrations
                            </TabsTrigger>
                            <TabsTrigger value="advanced" className="gap-2">
                                <Shield className="h-4 w-4" />
                                Advanced
                            </TabsTrigger>
                        </TabsList>

                        {/* General Tab */}
                        <TabsContent value="general" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Globe className="h-5 w-5" />
                                        Basic Meta Tags
                                    </CardTitle>
                                    <CardDescription>
                                        Configure the default meta tags that appear in search engine results
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="site_title">
                                                Site Title
                                                <FieldTooltip content="The default title of your website. This appears in browser tabs and search results." />
                                            </Label>
                                            <Input
                                                id="site_title"
                                                value={data.site_title || ''}
                                                onChange={(e) => setData('site_title', e.target.value)}
                                                placeholder="VIETFLOW TMS"
                                            />
                                            {errors.site_title && (
                                                <p className="text-sm text-red-600">{errors.site_title}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="title_separator">
                                                Title Separator
                                                <FieldTooltip content="Character used to separate page title from site title (e.g., Page | Site Name)" />
                                            </Label>
                                            <Select
                                                value={data.title_separator}
                                                onValueChange={(value) => setData('title_separator', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="|">| (Pipe)</SelectItem>
                                                    <SelectItem value="-">- (Dash)</SelectItem>
                                                    <SelectItem value="–">– (En Dash)</SelectItem>
                                                    <SelectItem value="•">• (Bullet)</SelectItem>
                                                    <SelectItem value="›">› (Arrow)</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="meta_description">
                                                Meta Description
                                                <FieldTooltip content="A brief summary of your site (150-160 characters recommended). This appears in search results." />
                                            </Label>
                                            <Textarea
                                                id="meta_description"
                                                value={data.meta_description || ''}
                                                onChange={(e) => setData('meta_description', e.target.value)}
                                                placeholder="A brief description of your website..."
                                                rows={3}
                                            />
                                            <p className="text-xs text-gray-500">
                                                {(data.meta_description || '').length}/160 characters
                                            </p>
                                            {errors.meta_description && (
                                                <p className="text-sm text-red-600">{errors.meta_description}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="meta_keywords">
                                                Meta Keywords
                                                <FieldTooltip content="Comma-separated keywords relevant to your site. Less important for SEO now but still used by some search engines." />
                                            </Label>
                                            <Input
                                                id="meta_keywords"
                                                value={data.meta_keywords || ''}
                                                onChange={(e) => setData('meta_keywords', e.target.value)}
                                                placeholder="keyword1, keyword2, keyword3"
                                            />
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="canonical_url">
                                                Canonical URL
                                                <FieldTooltip content="The preferred URL for your website. Helps prevent duplicate content issues." />
                                            </Label>
                                            <Input
                                                id="canonical_url"
                                                value={data.canonical_url || ''}
                                                onChange={(e) => setData('canonical_url', e.target.value)}
                                                placeholder="https://yourdomain.com"
                                                type="url"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Social Media Tab */}
                        <TabsContent value="social" className="space-y-6">
                            {/* Open Graph */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Share2 className="h-5 w-5" />
                                        Open Graph (Facebook, LinkedIn, Zalo)
                                    </CardTitle>
                                    <CardDescription>
                                        Control how your content appears when shared on Facebook, LinkedIn, and other
                                        platforms
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="og_site_name">
                                                Site Name
                                                <FieldTooltip content="The name of your website as displayed on social media" />
                                            </Label>
                                            <Input
                                                id="og_site_name"
                                                value={data.og_site_name || ''}
                                                onChange={(e) => setData('og_site_name', e.target.value)}
                                                placeholder="VIETFLOW TMS"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="og_type">
                                                Content Type
                                                <FieldTooltip content="The type of content on your site" />
                                            </Label>
                                            <Select
                                                value={data.og_type}
                                                onValueChange={(value) => setData('og_type', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="website">Website</SelectItem>
                                                    <SelectItem value="article">Article</SelectItem>
                                                    <SelectItem value="product">Product</SelectItem>
                                                    <SelectItem value="business.business">Business</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="og_title">
                                                Default Title
                                                <FieldTooltip content="The title shown when your pages are shared" />
                                            </Label>
                                            <Input
                                                id="og_title"
                                                value={data.og_title || ''}
                                                onChange={(e) => setData('og_title', e.target.value)}
                                                placeholder="Your page title"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="og_locale">
                                                Locale
                                                <FieldTooltip content="The language and region for your content" />
                                            </Label>
                                            <Select
                                                value={data.og_locale}
                                                onValueChange={(value) => setData('og_locale', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="vi_VN">Vietnamese (Vietnam)</SelectItem>
                                                    <SelectItem value="en_US">English (US)</SelectItem>
                                                    <SelectItem value="en_GB">English (UK)</SelectItem>
                                                    <SelectItem value="zh_CN">Chinese (Simplified)</SelectItem>
                                                    <SelectItem value="ja_JP">Japanese</SelectItem>
                                                    <SelectItem value="ko_KR">Korean</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="og_description">
                                                Default Description
                                                <FieldTooltip content="The description shown when your pages are shared" />
                                            </Label>
                                            <Textarea
                                                id="og_description"
                                                value={data.og_description || ''}
                                                onChange={(e) => setData('og_description', e.target.value)}
                                                placeholder="Description for social sharing..."
                                                rows={2}
                                            />
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="og_image">
                                                Default Image URL
                                                <FieldTooltip content="URL of the image shown when sharing. Recommended size: 1200x630px" />
                                            </Label>
                                            <Input
                                                id="og_image"
                                                value={data.og_image || ''}
                                                onChange={(e) => setData('og_image', e.target.value)}
                                                placeholder="https://yourdomain.com/og-image.jpg"
                                                type="url"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="og_image_width">Image Width (px)</Label>
                                            <Input
                                                id="og_image_width"
                                                type="number"
                                                value={data.og_image_width || ''}
                                                onChange={(e) =>
                                                    setData(
                                                        'og_image_width',
                                                        e.target.value ? parseInt(e.target.value) : null,
                                                    )
                                                }
                                                placeholder="1200"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="og_image_height">Image Height (px)</Label>
                                            <Input
                                                id="og_image_height"
                                                type="number"
                                                value={data.og_image_height || ''}
                                                onChange={(e) =>
                                                    setData(
                                                        'og_image_height',
                                                        e.target.value ? parseInt(e.target.value) : null,
                                                    )
                                                }
                                                placeholder="630"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Twitter Cards */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                                        </svg>
                                        Twitter Cards
                                    </CardTitle>
                                    <CardDescription>
                                        Configure how your content appears when shared on Twitter/X
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="twitter_card_type">
                                                Card Type
                                                <FieldTooltip content="The style of Twitter Card to use" />
                                            </Label>
                                            <Select
                                                value={data.twitter_card_type}
                                                onValueChange={(value) => setData('twitter_card_type', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="summary">Summary</SelectItem>
                                                    <SelectItem value="summary_large_image">
                                                        Summary with Large Image
                                                    </SelectItem>
                                                    <SelectItem value="app">App</SelectItem>
                                                    <SelectItem value="player">Player</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="twitter_site">
                                                Site @username
                                                <FieldTooltip content="Your website's Twitter handle (e.g., @yourcompany)" />
                                            </Label>
                                            <Input
                                                id="twitter_site"
                                                value={data.twitter_site || ''}
                                                onChange={(e) => setData('twitter_site', e.target.value)}
                                                placeholder="@yourcompany"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="twitter_creator">
                                                Creator @username
                                                <FieldTooltip content="The Twitter handle of the content creator" />
                                            </Label>
                                            <Input
                                                id="twitter_creator"
                                                value={data.twitter_creator || ''}
                                                onChange={(e) => setData('twitter_creator', e.target.value)}
                                                placeholder="@creator"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="twitter_title">Default Title</Label>
                                            <Input
                                                id="twitter_title"
                                                value={data.twitter_title || ''}
                                                onChange={(e) => setData('twitter_title', e.target.value)}
                                                placeholder="Title for Twitter sharing"
                                            />
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="twitter_description">Default Description</Label>
                                            <Textarea
                                                id="twitter_description"
                                                value={data.twitter_description || ''}
                                                onChange={(e) => setData('twitter_description', e.target.value)}
                                                placeholder="Description for Twitter sharing..."
                                                rows={2}
                                            />
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="twitter_image">Default Image URL</Label>
                                            <Input
                                                id="twitter_image"
                                                value={data.twitter_image || ''}
                                                onChange={(e) => setData('twitter_image', e.target.value)}
                                                placeholder="https://yourdomain.com/twitter-image.jpg"
                                                type="url"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Indexing Tab */}
                        <TabsContent value="indexing" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Search className="h-5 w-5" />
                                        Robots Meta Directives
                                    </CardTitle>
                                    <CardDescription>
                                        Control how search engines index and display your website
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>Allow Indexing</Label>
                                                <p className="text-sm text-gray-500">
                                                    Allow search engines to index your pages
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.robots_index}
                                                onCheckedChange={(checked) => setData('robots_index', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>Follow Links</Label>
                                                <p className="text-sm text-gray-500">
                                                    Allow search engines to follow links on your pages
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.robots_follow}
                                                onCheckedChange={(checked) => setData('robots_follow', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>No Archive</Label>
                                                <p className="text-sm text-gray-500">
                                                    Prevent search engines from caching pages
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.robots_noarchive}
                                                onCheckedChange={(checked) => setData('robots_noarchive', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>No Snippet</Label>
                                                <p className="text-sm text-gray-500">
                                                    Prevent search engines from showing snippets
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.robots_nosnippet}
                                                onCheckedChange={(checked) => setData('robots_nosnippet', checked)}
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="robots_max_snippet">
                                                Max Snippet Length
                                                <FieldTooltip content="Maximum characters for search snippets (-1 for unlimited)" />
                                            </Label>
                                            <Input
                                                id="robots_max_snippet"
                                                type="number"
                                                value={data.robots_max_snippet || ''}
                                                onChange={(e) =>
                                                    setData(
                                                        'robots_max_snippet',
                                                        e.target.value ? parseInt(e.target.value) : null,
                                                    )
                                                }
                                                placeholder="-1"
                                                min="-1"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="robots_max_image_preview">Max Image Preview</Label>
                                            <Select
                                                value={data.robots_max_image_preview}
                                                onValueChange={(value) => setData('robots_max_image_preview', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="none">None</SelectItem>
                                                    <SelectItem value="standard">Standard</SelectItem>
                                                    <SelectItem value="large">Large</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="robots_max_video_preview">
                                                Max Video Preview (seconds)
                                            </Label>
                                            <Input
                                                id="robots_max_video_preview"
                                                type="number"
                                                value={data.robots_max_video_preview || ''}
                                                onChange={(e) =>
                                                    setData(
                                                        'robots_max_video_preview',
                                                        e.target.value ? parseInt(e.target.value) : null,
                                                    )
                                                }
                                                placeholder="-1"
                                                min="-1"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="googlebot_specific">
                                                Googlebot Specific Directives
                                                <FieldTooltip content="Additional directives specifically for Google crawler" />
                                            </Label>
                                            <Textarea
                                                id="googlebot_specific"
                                                value={data.googlebot_specific || ''}
                                                onChange={(e) => setData('googlebot_specific', e.target.value)}
                                                placeholder="e.g., notranslate, unavailable_after: 2025-01-01"
                                                rows={2}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="bingbot_specific">
                                                Bingbot Specific Directives
                                                <FieldTooltip content="Additional directives specifically for Bing crawler" />
                                            </Label>
                                            <Textarea
                                                id="bingbot_specific"
                                                value={data.bingbot_specific || ''}
                                                onChange={(e) => setData('bingbot_specific', e.target.value)}
                                                placeholder="Additional Bing directives..."
                                                rows={2}
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Schema Markup Tab */}
                        <TabsContent value="schema" className="space-y-6">
                            {/* Organization Schema */}
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle className="flex items-center gap-2">
                                                <Code className="h-5 w-5" />
                                                Organization Schema
                                            </CardTitle>
                                            <CardDescription>
                                                Add structured data about your organization for rich search results
                                            </CardDescription>
                                        </div>
                                        <Switch
                                            checked={data.schema_organization_enabled}
                                            onCheckedChange={(checked) =>
                                                setData('schema_organization_enabled', checked)
                                            }
                                        />
                                    </div>
                                </CardHeader>
                                {data.schema_organization_enabled && (
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="schema_organization_name">Organization Name</Label>
                                                <Input
                                                    id="schema_organization_name"
                                                    value={data.schema_organization_name || ''}
                                                    onChange={(e) =>
                                                        setData('schema_organization_name', e.target.value)
                                                    }
                                                    placeholder="VIETFLOW LLC"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_organization_url">Website URL</Label>
                                                <Input
                                                    id="schema_organization_url"
                                                    value={data.schema_organization_url || ''}
                                                    onChange={(e) => setData('schema_organization_url', e.target.value)}
                                                    placeholder="https://yourdomain.com"
                                                    type="url"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_organization_logo">Logo URL</Label>
                                                <Input
                                                    id="schema_organization_logo"
                                                    value={data.schema_organization_logo || ''}
                                                    onChange={(e) =>
                                                        setData('schema_organization_logo', e.target.value)
                                                    }
                                                    placeholder="https://yourdomain.com/logo.png"
                                                    type="url"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_organization_contact_type">Contact Type</Label>
                                                <Select
                                                    value={data.schema_organization_contact_type || ''}
                                                    onValueChange={(value) =>
                                                        setData('schema_organization_contact_type', value)
                                                    }
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select contact type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="customer service">Customer Service</SelectItem>
                                                        <SelectItem value="technical support">Technical Support</SelectItem>
                                                        <SelectItem value="sales">Sales</SelectItem>
                                                        <SelectItem value="billing support">Billing Support</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_organization_phone">Phone Number</Label>
                                                <Input
                                                    id="schema_organization_phone"
                                                    value={data.schema_organization_phone || ''}
                                                    onChange={(e) =>
                                                        setData('schema_organization_phone', e.target.value)
                                                    }
                                                    placeholder="+84-123-456-789"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_organization_email">Email</Label>
                                                <Input
                                                    id="schema_organization_email"
                                                    value={data.schema_organization_email || ''}
                                                    onChange={(e) =>
                                                        setData('schema_organization_email', e.target.value)
                                                    }
                                                    placeholder="<EMAIL>"
                                                    type="email"
                                                />
                                            </div>

                                            <div className="space-y-2 md:col-span-2">
                                                <Label htmlFor="schema_organization_address">Address</Label>
                                                <Textarea
                                                    id="schema_organization_address"
                                                    value={data.schema_organization_address || ''}
                                                    onChange={(e) =>
                                                        setData('schema_organization_address', e.target.value)
                                                    }
                                                    placeholder="123 Street Name, City, Country"
                                                    rows={2}
                                                />
                                            </div>

                                            <div className="space-y-2 md:col-span-2">
                                                <Label>
                                                    Social Profiles
                                                    <FieldTooltip content="Add URLs to your social media profiles" />
                                                </Label>
                                                <div className="space-y-2">
                                                    {socialProfiles.map((profile, index) => (
                                                        <div key={index} className="flex gap-2">
                                                            <Input
                                                                value={profile}
                                                                onChange={(e) =>
                                                                    updateSocialProfile(index, e.target.value)
                                                                }
                                                                placeholder="https://facebook.com/yourpage"
                                                                type="url"
                                                            />
                                                            {socialProfiles.length > 1 && (
                                                                <Button
                                                                    type="button"
                                                                    variant="outline"
                                                                    size="icon"
                                                                    onClick={() => removeSocialProfile(index)}
                                                                >
                                                                    ×
                                                                </Button>
                                                            )}
                                                        </div>
                                                    ))}
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={addSocialProfile}
                                                    >
                                                        + Add Social Profile
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                )}
                            </Card>

                            {/* Website Schema */}
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle className="flex items-center gap-2">
                                                <Globe className="h-5 w-5" />
                                                Website Schema
                                            </CardTitle>
                                            <CardDescription>
                                                Enable Sitelinks Search Box and website structured data
                                            </CardDescription>
                                        </div>
                                        <Switch
                                            checked={data.schema_website_enabled}
                                            onCheckedChange={(checked) => setData('schema_website_enabled', checked)}
                                        />
                                    </div>
                                </CardHeader>
                                {data.schema_website_enabled && (
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="schema_website_name">Website Name</Label>
                                                <Input
                                                    id="schema_website_name"
                                                    value={data.schema_website_name || ''}
                                                    onChange={(e) => setData('schema_website_name', e.target.value)}
                                                    placeholder="VIETFLOW TMS"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_website_alternate_name">Alternate Name</Label>
                                                <Input
                                                    id="schema_website_alternate_name"
                                                    value={data.schema_website_alternate_name || ''}
                                                    onChange={(e) =>
                                                        setData('schema_website_alternate_name', e.target.value)
                                                    }
                                                    placeholder="VIETFLOW"
                                                />
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>Enable Sitelinks Search Box</Label>
                                                <p className="text-sm text-gray-500">
                                                    Show a search box in Google search results
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.schema_search_enabled}
                                                onCheckedChange={(checked) => setData('schema_search_enabled', checked)}
                                            />
                                        </div>

                                        {data.schema_search_enabled && (
                                            <div className="space-y-2">
                                                <Label htmlFor="schema_search_url_template">
                                                    Search URL Template
                                                    <FieldTooltip content='Use {search_term_string} as placeholder. Example: https://yourdomain.com/search?q={search_term_string}' />
                                                </Label>
                                                <Input
                                                    id="schema_search_url_template"
                                                    value={data.schema_search_url_template || ''}
                                                    onChange={(e) =>
                                                        setData('schema_search_url_template', e.target.value)
                                                    }
                                                    placeholder="https://yourdomain.com/search?q={search_term_string}"
                                                />
                                            </div>
                                        )}
                                    </CardContent>
                                )}
                            </Card>

                            {/* Local Business Schema */}
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle className="flex items-center gap-2">
                                                <FileText className="h-5 w-5" />
                                                Local Business Schema
                                            </CardTitle>
                                            <CardDescription>
                                                Add structured data for local business search results
                                            </CardDescription>
                                        </div>
                                        <Switch
                                            checked={data.schema_local_business_enabled}
                                            onCheckedChange={(checked) =>
                                                setData('schema_local_business_enabled', checked)
                                            }
                                        />
                                    </div>
                                </CardHeader>
                                {data.schema_local_business_enabled && (
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="schema_local_business_type">Business Type</Label>
                                                <Select
                                                    value={data.schema_local_business_type}
                                                    onValueChange={(value) =>
                                                        setData('schema_local_business_type', value)
                                                    }
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="LocalBusiness">Local Business</SelectItem>
                                                        <SelectItem value="Store">Store</SelectItem>
                                                        <SelectItem value="Restaurant">Restaurant</SelectItem>
                                                        <SelectItem value="ProfessionalService">
                                                            Professional Service
                                                        </SelectItem>
                                                        <SelectItem value="MovingCompany">Moving Company</SelectItem>
                                                        <SelectItem value="TransportCompany">
                                                            Transport Company
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_local_business_name">Business Name</Label>
                                                <Input
                                                    id="schema_local_business_name"
                                                    value={data.schema_local_business_name || ''}
                                                    onChange={(e) =>
                                                        setData('schema_local_business_name', e.target.value)
                                                    }
                                                    placeholder="Your Business Name"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_local_business_image">Image URL</Label>
                                                <Input
                                                    id="schema_local_business_image"
                                                    value={data.schema_local_business_image || ''}
                                                    onChange={(e) =>
                                                        setData('schema_local_business_image', e.target.value)
                                                    }
                                                    placeholder="https://yourdomain.com/business-image.jpg"
                                                    type="url"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_local_business_price_range">
                                                    Price Range
                                                    <FieldTooltip content="Use $ symbols to indicate price range (e.g., $, $$, $$$)" />
                                                </Label>
                                                <Select
                                                    value={data.schema_local_business_price_range || ''}
                                                    onValueChange={(value) =>
                                                        setData('schema_local_business_price_range', value)
                                                    }
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select price range" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="$">$ (Budget)</SelectItem>
                                                        <SelectItem value="$$">$$ (Moderate)</SelectItem>
                                                        <SelectItem value="$$$">$$$ (Expensive)</SelectItem>
                                                        <SelectItem value="$$$$">$$$$ (Very Expensive)</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_local_business_geo_lat">Latitude</Label>
                                                <Input
                                                    id="schema_local_business_geo_lat"
                                                    type="number"
                                                    step="any"
                                                    value={data.schema_local_business_geo_lat || ''}
                                                    onChange={(e) =>
                                                        setData(
                                                            'schema_local_business_geo_lat',
                                                            e.target.value ? parseFloat(e.target.value) : null,
                                                        )
                                                    }
                                                    placeholder="10.762622"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="schema_local_business_geo_lng">Longitude</Label>
                                                <Input
                                                    id="schema_local_business_geo_lng"
                                                    type="number"
                                                    step="any"
                                                    value={data.schema_local_business_geo_lng || ''}
                                                    onChange={(e) =>
                                                        setData(
                                                            'schema_local_business_geo_lng',
                                                            e.target.value ? parseFloat(e.target.value) : null,
                                                        )
                                                    }
                                                    placeholder="106.660172"
                                                />
                                            </div>
                                        </div>
                                    </CardContent>
                                )}
                            </Card>
                        </TabsContent>

                        {/* Integrations Tab */}
                        <TabsContent value="integrations" className="space-y-6">
                            {/* Site Verification */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Shield className="h-5 w-5" />
                                        Site Verification
                                    </CardTitle>
                                    <CardDescription>
                                        Add verification codes from search engines and social platforms
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="google_site_verification">
                                                Google Site Verification
                                                <FieldTooltip content="Get this from Google Search Console" />
                                            </Label>
                                            <Input
                                                id="google_site_verification"
                                                value={data.google_site_verification || ''}
                                                onChange={(e) => setData('google_site_verification', e.target.value)}
                                                placeholder="google-site-verification code"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="bing_site_verification">
                                                Bing Site Verification
                                                <FieldTooltip content="Get this from Bing Webmaster Tools" />
                                            </Label>
                                            <Input
                                                id="bing_site_verification"
                                                value={data.bing_site_verification || ''}
                                                onChange={(e) => setData('bing_site_verification', e.target.value)}
                                                placeholder="bing-site-verification code"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="yandex_verification">Yandex Verification</Label>
                                            <Input
                                                id="yandex_verification"
                                                value={data.yandex_verification || ''}
                                                onChange={(e) => setData('yandex_verification', e.target.value)}
                                                placeholder="yandex-verification code"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="pinterest_verification">Pinterest Verification</Label>
                                            <Input
                                                id="pinterest_verification"
                                                value={data.pinterest_verification || ''}
                                                onChange={(e) => setData('pinterest_verification', e.target.value)}
                                                placeholder="pinterest-verification code"
                                            />
                                        </div>

                                        <div className="space-y-2 md:col-span-2">
                                            <Label htmlFor="facebook_domain_verification">
                                                Facebook Domain Verification
                                            </Label>
                                            <Input
                                                id="facebook_domain_verification"
                                                value={data.facebook_domain_verification || ''}
                                                onChange={(e) => setData('facebook_domain_verification', e.target.value)}
                                                placeholder="facebook-domain-verification code"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Analytics */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <BarChart3 className="h-5 w-5" />
                                        Analytics & Tracking
                                    </CardTitle>
                                    <CardDescription>
                                        Configure analytics and tracking pixel integrations
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="google_analytics_id">
                                                Google Analytics 4 ID
                                                <FieldTooltip content="Your GA4 Measurement ID (e.g., G-XXXXXXXXXX)" />
                                            </Label>
                                            <Input
                                                id="google_analytics_id"
                                                value={data.google_analytics_id || ''}
                                                onChange={(e) => setData('google_analytics_id', e.target.value)}
                                                placeholder="G-XXXXXXXXXX"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="google_tag_manager_id">
                                                Google Tag Manager ID
                                                <FieldTooltip content="Your GTM Container ID (e.g., GTM-XXXXXXX)" />
                                            </Label>
                                            <Input
                                                id="google_tag_manager_id"
                                                value={data.google_tag_manager_id || ''}
                                                onChange={(e) => setData('google_tag_manager_id', e.target.value)}
                                                placeholder="GTM-XXXXXXX"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="facebook_pixel_id">
                                                Facebook Pixel ID
                                                <FieldTooltip content="Your Facebook Pixel ID for conversion tracking" />
                                            </Label>
                                            <Input
                                                id="facebook_pixel_id"
                                                value={data.facebook_pixel_id || ''}
                                                onChange={(e) => setData('facebook_pixel_id', e.target.value)}
                                                placeholder="XXXXXXXXXXXXXXXX"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="tiktok_pixel_id">
                                                TikTok Pixel ID
                                                <FieldTooltip content="Your TikTok Pixel ID for conversion tracking" />
                                            </Label>
                                            <Input
                                                id="tiktok_pixel_id"
                                                value={data.tiktok_pixel_id || ''}
                                                onChange={(e) => setData('tiktok_pixel_id', e.target.value)}
                                                placeholder="XXXXXXXXXXXXXXXXXX"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Advanced Tab */}
                        <TabsContent value="advanced" className="space-y-6">
                            {/* Custom Scripts */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Code className="h-5 w-5" />
                                        Custom Scripts
                                    </CardTitle>
                                    <CardDescription>
                                        Add custom HTML, JavaScript, or CSS to specific locations in your pages
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="custom_head_scripts">
                                            Head Scripts
                                            <FieldTooltip content="Scripts injected before </head> tag" />
                                        </Label>
                                        <Textarea
                                            id="custom_head_scripts"
                                            value={data.custom_head_scripts || ''}
                                            onChange={(e) => setData('custom_head_scripts', e.target.value)}
                                            placeholder="<!-- Custom head scripts -->"
                                            rows={4}
                                            className="font-mono text-sm"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="custom_body_start_scripts">
                                            Body Start Scripts
                                            <FieldTooltip content='Scripts injected right after <body> tag' />
                                        </Label>
                                        <Textarea
                                            id="custom_body_start_scripts"
                                            value={data.custom_body_start_scripts || ''}
                                            onChange={(e) => setData('custom_body_start_scripts', e.target.value)}
                                            placeholder="<!-- Custom body start scripts -->"
                                            rows={4}
                                            className="font-mono text-sm"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="custom_body_end_scripts">
                                            Body End Scripts
                                            <FieldTooltip content="Scripts injected before </body> tag" />
                                        </Label>
                                        <Textarea
                                            id="custom_body_end_scripts"
                                            value={data.custom_body_end_scripts || ''}
                                            onChange={(e) => setData('custom_body_end_scripts', e.target.value)}
                                            placeholder="<!-- Custom body end scripts -->"
                                            rows={4}
                                            className="font-mono text-sm"
                                        />
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Sitemap & Robots.txt */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Sitemap & Robots.txt
                                    </CardTitle>
                                    <CardDescription>Configure sitemap generation and robots.txt file</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>Enable Sitemap</Label>
                                                <p className="text-sm text-gray-500">
                                                    Generate dynamic sitemap.xml
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.sitemap_enabled}
                                                onCheckedChange={(checked) => setData('sitemap_enabled', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>Include Images</Label>
                                                <p className="text-sm text-gray-500">Add image tags to sitemap</p>
                                            </div>
                                            <Switch
                                                checked={data.sitemap_include_images}
                                                onCheckedChange={(checked) => setData('sitemap_include_images', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <Label>Include Last Modified</Label>
                                                <p className="text-sm text-gray-500">Add lastmod timestamps</p>
                                            </div>
                                            <Switch
                                                checked={data.sitemap_include_lastmod}
                                                onCheckedChange={(checked) =>
                                                    setData('sitemap_include_lastmod', checked)
                                                }
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="sitemap_changefreq_default">Default Change Frequency</Label>
                                            <Select
                                                value={data.sitemap_changefreq_default}
                                                onValueChange={(value) => setData('sitemap_changefreq_default', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="always">Always</SelectItem>
                                                    <SelectItem value="hourly">Hourly</SelectItem>
                                                    <SelectItem value="daily">Daily</SelectItem>
                                                    <SelectItem value="weekly">Weekly</SelectItem>
                                                    <SelectItem value="monthly">Monthly</SelectItem>
                                                    <SelectItem value="yearly">Yearly</SelectItem>
                                                    <SelectItem value="never">Never</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="sitemap_priority_default">
                                                Default Priority (0.0 - 1.0)
                                            </Label>
                                            <Input
                                                id="sitemap_priority_default"
                                                type="number"
                                                step="0.1"
                                                min="0"
                                                max="1"
                                                value={data.sitemap_priority_default}
                                                onChange={(e) =>
                                                    setData('sitemap_priority_default', parseFloat(e.target.value))
                                                }
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="robots_txt_sitemap_url">Sitemap URL in Robots.txt</Label>
                                        <Input
                                            id="robots_txt_sitemap_url"
                                            value={data.robots_txt_sitemap_url || ''}
                                            onChange={(e) => setData('robots_txt_sitemap_url', e.target.value)}
                                            placeholder="https://yourdomain.com/sitemap.xml"
                                            type="url"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="robots_txt_content">
                                            Custom Robots.txt Content
                                            <FieldTooltip content="Leave empty to use auto-generated content" />
                                        </Label>
                                        <Textarea
                                            id="robots_txt_content"
                                            value={data.robots_txt_content || ''}
                                            onChange={(e) => setData('robots_txt_content', e.target.value)}
                                            placeholder={`User-agent: *\nAllow: /\n\nDisallow: /admin\nDisallow: /api/`}
                                            rows={6}
                                            className="font-mono text-sm"
                                        />
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Other Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5" />
                                        Other Settings
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="default_og_image_fallback">
                                                Default OG Image Fallback
                                                <FieldTooltip content="Used when a page doesn't have its own OG image" />
                                            </Label>
                                            <Input
                                                id="default_og_image_fallback"
                                                value={data.default_og_image_fallback || ''}
                                                onChange={(e) => setData('default_og_image_fallback', e.target.value)}
                                                placeholder="https://yourdomain.com/default-og.jpg"
                                                type="url"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="description_max_length">
                                                Description Max Length
                                                <FieldTooltip content="Maximum characters for auto-generated descriptions" />
                                            </Label>
                                            <Input
                                                id="description_max_length"
                                                type="number"
                                                value={data.description_max_length}
                                                onChange={(e) =>
                                                    setData('description_max_length', parseInt(e.target.value) || 160)
                                                }
                                                min="50"
                                                max="500"
                                            />
                                        </div>
                                    </div>

                                    <div className="flex items-center justify-between rounded-lg border p-4">
                                        <div className="space-y-0.5">
                                            <Label>Auto-generate Description</Label>
                                            <p className="text-sm text-gray-500">
                                                Automatically create meta descriptions from page content
                                            </p>
                                        </div>
                                        <Switch
                                            checked={data.auto_generate_description}
                                            onCheckedChange={(checked) => setData('auto_generate_description', checked)}
                                        />
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Warning */}
                            <Card className="border-amber-200 bg-amber-50">
                                <CardContent className="pt-6">
                                    <div className="flex gap-3">
                                        <AlertCircle className="h-5 w-5 text-amber-600" />
                                        <div className="space-y-1 text-sm">
                                            <p className="font-semibold text-amber-900">Important Notes:</p>
                                            <ul className="list-inside list-disc space-y-1 text-amber-800">
                                                <li>Custom scripts should be tested thoroughly before deployment</li>
                                                <li>Invalid robots.txt content can negatively impact SEO</li>
                                                <li>
                                                    Changes to indexing settings may take time to reflect in search
                                                    engines
                                                </li>
                                                <li>Verify all tracking codes are correct to ensure proper analytics</li>
                                            </ul>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </form>
            </div>
        </AuthenticatedLayout>
    );
}
