<?php

namespace App\Policies\Organizations;

use App\Enums\Permission;
use App\Models\Organizations\SeoSetting;
use App\Models\User;

class SeoSettingPolicy
{
    /**
     * Determine whether the user can view SEO settings.
     *
     * Currently allows all authenticated organization users.
     * Routes are already protected by auth/verified/organization-assigned middleware.
     *
     * To restrict to specific roles, replace with:
     * return $user->can(Permission::ORGANIZATION_MANAGER);
     */
    public function view(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update SEO settings.
     *
     * Currently allows all authenticated organization users.
     * Routes are already protected by auth/verified/organization-assigned middleware.
     *
     * To restrict to specific roles, replace with:
     * return $user->can(Permission::ORGANIZATION_MANAGER);
     */
    public function update(User $user): bool
    {
        return true;
    }
}
