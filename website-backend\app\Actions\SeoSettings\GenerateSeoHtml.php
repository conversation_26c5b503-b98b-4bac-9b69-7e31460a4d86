<?php

namespace App\Actions\SeoSettings;

use App\Models\Organizations\SeoSetting;
use Illuminate\Support\Facades\Log;

class GenerateSeoHtml
{
    /**
     * Generate static index.html with SEO meta tags for the frontend
     *
     * This overwrites the frontend's index.html with server-rendered SEO tags
     * so that search engine crawlers can see the content immediately.
     */
    public function execute(?SeoSetting $seoSetting = null): bool
    {
        try {
            // Get SEO settings
            if (!$seoSetting) {
                $seoSetting = SeoSetting::first();
            }

            if (!$seoSetting) {
                Log::warning('No SEO settings found, using defaults');
            }

            // Path to frontend index.html
            $frontendPath = base_path('../website-frontend/index.html');

            if (!file_exists(dirname($frontendPath))) {
                Log::error('Frontend directory not found: ' . dirname($frontendPath));
                return false;
            }

            // Build HTML with SEO tags
            $html = $this->buildHtml($seoSetting);

            // Write to file
            $result = file_put_contents($frontendPath, $html);

            if ($result === false) {
                Log::error('Failed to write SEO HTML to: ' . $frontendPath);
                return false;
            }

            Log::info('Successfully generated SEO HTML at: ' . $frontendPath);
            return true;

        } catch (\Exception $e) {
            Log::error('Error generating SEO HTML: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Build the complete HTML with SEO meta tags
     * All user input is escaped to prevent XSS attacks
     */
    private function buildHtml(?SeoSetting $seo): string
    {
        // Escape all user input to prevent XSS
        $title = htmlspecialchars($seo?->site_title ?? 'VIETFLOW LLC', ENT_QUOTES, 'UTF-8');
        $description = htmlspecialchars($seo?->meta_description ?? 'Professional logistics and shipping services.', ENT_QUOTES, 'UTF-8');
        $keywords = htmlspecialchars($seo?->meta_keywords ?? '', ENT_QUOTES, 'UTF-8');
        $robots = htmlspecialchars($seo?->robots ?? 'index, follow', ENT_QUOTES, 'UTF-8');

        // Open Graph - all escaped
        $ogType = htmlspecialchars($seo?->og_type ?? 'website', ENT_QUOTES, 'UTF-8');
        $ogTitle = htmlspecialchars($seo?->og_title ?? $title, ENT_QUOTES, 'UTF-8');
        $ogDescription = htmlspecialchars($seo?->og_description ?? $description, ENT_QUOTES, 'UTF-8');
        $ogSiteName = htmlspecialchars($seo?->og_site_name ?? $title, ENT_QUOTES, 'UTF-8');
        $ogImage = htmlspecialchars($seo?->og_image ?? '', ENT_QUOTES, 'UTF-8');
        $ogImageWidth = htmlspecialchars($seo?->og_image_width ?? '', ENT_QUOTES, 'UTF-8');
        $ogImageHeight = htmlspecialchars($seo?->og_image_height ?? '', ENT_QUOTES, 'UTF-8');
        $ogLocale = htmlspecialchars($seo?->og_locale ?? 'vi_VN', ENT_QUOTES, 'UTF-8');

        // Twitter - all escaped
        $twitterCard = htmlspecialchars($seo?->twitter_card ?? 'summary_large_image', ENT_QUOTES, 'UTF-8');
        $twitterSite = htmlspecialchars($seo?->twitter_site ?? '', ENT_QUOTES, 'UTF-8');
        $twitterCreator = htmlspecialchars($seo?->twitter_creator ?? '', ENT_QUOTES, 'UTF-8');
        $twitterTitle = htmlspecialchars($seo?->twitter_title ?? $ogTitle, ENT_QUOTES, 'UTF-8');
        $twitterDescription = htmlspecialchars($seo?->twitter_description ?? $ogDescription, ENT_QUOTES, 'UTF-8');
        $twitterImage = htmlspecialchars($seo?->twitter_image ?? $ogImage, ENT_QUOTES, 'UTF-8');

        // Verification codes - escaped
        $googleVerification = htmlspecialchars($seo?->google_site_verification ?? '', ENT_QUOTES, 'UTF-8');
        $bingVerification = htmlspecialchars($seo?->bing_site_verification ?? '', ENT_QUOTES, 'UTF-8');

        // FIXED: Use correct field names from SeoSetting model
        $headScripts = $seo?->custom_head_scripts ?? '';
        $bodyStartScripts = $seo?->custom_body_start_scripts ?? '';
        $bodyEndScripts = $seo?->custom_body_end_scripts ?? '';

        $html = <<<HTML
<!DOCTYPE html>
<html lang="vi">
   <head>
      <meta charset="UTF-8" />
      <meta http-equiv="X-UA-Compatible" content="IE=edge" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <link rel="icon" type="image/png" href="/favicon.png" />
      <link rel="shortcut icon" type="image/png" href="/favicon.png" />
      <link rel="apple-touch-icon" href="/favicon.png" />

      <!-- Primary Meta Tags -->
      <title>{$title}</title>
      <meta name="title" content="{$title}" />
      <meta name="description" content="{$description}" />
HTML;

        if ($keywords) {
            $html .= "\n      <meta name=\"keywords\" content=\"{$keywords}\" />";
        }

        $html .= "\n      <meta name=\"robots\" content=\"{$robots}\" />\n";

        // Open Graph tags
        $html .= <<<HTML

      <!-- Open Graph / Facebook -->
      <meta property="og:type" content="{$ogType}" />
      <meta property="og:title" content="{$ogTitle}" />
      <meta property="og:description" content="{$ogDescription}" />
      <meta property="og:site_name" content="{$ogSiteName}" />
      <meta property="og:locale" content="{$ogLocale}" />
HTML;

        if ($ogImage) {
            $html .= "\n      <meta property=\"og:image\" content=\"{$ogImage}\" />";
            if ($ogImageWidth) {
                $html .= "\n      <meta property=\"og:image:width\" content=\"{$ogImageWidth}\" />";
            }
            if ($ogImageHeight) {
                $html .= "\n      <meta property=\"og:image:height\" content=\"{$ogImageHeight}\" />";
            }
        }

        // Twitter tags
        $html .= <<<HTML


      <!-- Twitter -->
      <meta name="twitter:card" content="{$twitterCard}" />
      <meta name="twitter:title" content="{$twitterTitle}" />
      <meta name="twitter:description" content="{$twitterDescription}" />
HTML;

        if ($twitterSite) {
            $html .= "\n      <meta name=\"twitter:site\" content=\"{$twitterSite}\" />";
        }
        if ($twitterCreator) {
            $html .= "\n      <meta name=\"twitter:creator\" content=\"{$twitterCreator}\" />";
        }
        if ($twitterImage) {
            $html .= "\n      <meta name=\"twitter:image\" content=\"{$twitterImage}\" />";
        }

        // Verification tags
        if ($googleVerification || $bingVerification) {
            $html .= "\n\n      <!-- Verification -->";
            if ($googleVerification) {
                $html .= "\n      <meta name=\"google-site-verification\" content=\"{$googleVerification}\" />";
            }
            if ($bingVerification) {
                $html .= "\n      <meta name=\"msvalidate.01\" content=\"{$bingVerification}\" />";
            }
        }

        // Custom head scripts
        if ($headScripts) {
            $html .= "\n\n      <!-- Custom Scripts -->\n      {$headScripts}";
        }

        $html .= <<<HTML

   </head>
   <body>
HTML;

        // Body start scripts
        if ($bodyStartScripts) {
            $html .= "\n      {$bodyStartScripts}";
        }

        $html .= <<<HTML

      <div id="vue-app"></div>
      <script type="module" src="./main.ts"></script>
HTML;

        // Body end scripts
        if ($bodyEndScripts) {
            $html .= "\n      {$bodyEndScripts}";
        }

        $html .= <<<HTML

   </body>
</html>
HTML;

        return $html;
    }
}
