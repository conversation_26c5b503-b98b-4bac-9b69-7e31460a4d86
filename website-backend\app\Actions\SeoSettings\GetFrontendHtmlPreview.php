<?php

namespace App\Actions\SeoSettings;

use App\Enums\Permission;
use App\Models\Organizations\SeoSetting;
use Exception;
use Illuminate\Support\Facades\Gate;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetFrontendHtmlPreview
{
    use AsAction;

    /**
     * Get the current generated HTML from website-frontend/index.html
     */
    public function handle(): array
    {
        $frontendPath = base_path('../website-frontend/index.html');

        // Validate path security: ensure it's the expected file
        $realPath = realpath($frontendPath);
        $expectedDir = realpath(base_path('../website-frontend'));

        if (!$realPath || !$expectedDir || strpos($realPath, $expectedDir) !== 0) {
            return [
                'success' => false,
                'message' => 'Invalid frontend path',
                'html' => null,
                'last_modified' => null,
            ];
        }

        if (!file_exists($frontendPath)) {
            return [
                'success' => false,
                'message' => 'Frontend HTML file not found',
                'html' => null,
                'last_modified' => null,
            ];
        }

        $html = @file_get_contents($frontendPath);
        if ($html === false) {
            return [
                'success' => false,
                'message' => 'Failed to read frontend HTML file',
                'html' => null,
                'last_modified' => null,
            ];
        }

        $lastModified = filemtime($frontendPath);

        // Extract key SEO meta tags for preview
        $title = $this->extractMetaTag($html, 'title');
        $description = $this->extractMetaTag($html, 'meta', 'name="description"');
        $ogTitle = $this->extractMetaTag($html, 'meta', 'property="og:title"');
        $twitterCard = $this->extractMetaTag($html, 'meta', 'name="twitter:card"');

        return [
            'success' => true,
            'html' => $html,
            'last_modified' => $lastModified,
            'last_modified_human' => date('Y-m-d H:i:s', $lastModified),
            'preview' => [
                'title' => $title,
                'description' => $description,
                'og_title' => $ogTitle,
                'twitter_card' => $twitterCard,
            ],
        ];
    }

    /**
     * Extract content from a meta tag using DOMDocument for reliable parsing
     */
    private function extractMetaTag(string $html, string $tag, string $attribute = null): ?string
    {
        // Suppress DOMDocument warnings for malformed HTML
        libxml_use_internal_errors(true);

        $dom = new \DOMDocument();
        $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        libxml_clear_errors();

        if ($tag === 'title') {
            $titles = $dom->getElementsByTagName('title');
            if ($titles->length > 0) {
                return trim($titles->item(0)->textContent);
            }
            return null;
        }

        if ($tag === 'meta' && $attribute) {
            // Parse attribute like 'name="description"' or 'property="og:title"'
            if (preg_match('/^(\w+)="([^"]+)"$/', $attribute, $matches)) {
                $attrName = $matches[1];
                $attrValue = $matches[2];

                $metas = $dom->getElementsByTagName('meta');
                foreach ($metas as $meta) {
                    if ($meta->getAttribute($attrName) === $attrValue) {
                        return trim($meta->getAttribute('content'));
                    }
                }
            }
        }

        return null;
    }

    public function asController(ActionRequest $request)
    {
        return $this->handle();
    }

    public function authorize(ActionRequest $request): bool
    {
        return Gate::allows('view', SeoSetting::class);
    }
}
