{"name": "Vietflow-logistics", "version": "1.0.0", "type": "module", "start-date": "11/1/2022", "description": "A full website for a logistics company in Lagos, Vietnam.", "main": "main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "coverage": "vitest run --coverage"}, "keywords": [], "author": "adebola-io", "license": "ISC", "devDependencies": {"@testing-library/vue": "^6.6.1", "@unhead/ssr": "^2.0.19", "@vitejs/plugin-vue": "^6.0.2", "@vitest/coverage-c8": "^0.24.5", "@vitest/coverage-v8": "^4.0.15", "@vue/compiler-sfc": "^3.2.41", "@vue/test-utils": "2.0.0-rc.18", "happy-dom": "^7.6.6", "tsx": "^4.21.0", "vite": "^7.2.6", "vitest": "^4.0.15"}, "dependencies": {"@pinia/testing": "^0.0.14", "@unhead/vue": "^2.0.19", "gsap": "^3.11.3", "pinia": "^2.0.23", "vue": "^3.2.41", "vue-router": "^4.1.6"}}