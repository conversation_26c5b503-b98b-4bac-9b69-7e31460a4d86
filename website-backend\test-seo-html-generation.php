<?php

require __DIR__ . '/vendor/autoload.php';

use App\Actions\SeoSettings\GenerateSeoHtml;
use App\Models\Organizations\SeoSetting;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing SEO HTML Generation...\n\n";

// Test 1: Get SEO settings
echo "1. Fetching SEO settings...\n";
$seoSetting = SeoSetting::first();

if ($seoSetting) {
    echo "   ✓ Found SEO settings: {$seoSetting->site_title}\n";
} else {
    echo "   ⚠ No SEO settings found, will use defaults\n";
}

// Test 2: Generate HTML
echo "\n2. Generating HTML...\n";
$generator = new GenerateSeoHtml();
$result = $generator->execute($seoSetting);

if ($result) {
    echo "   ✓ HTML generated successfully\n";
} else {
    echo "   ✗ Failed to generate HTML\n";
    exit(1);
}

// Test 3: Verify file exists
echo "\n3. Verifying generated file...\n";
$frontendPath = base_path('../website-frontend/index.html');

if (file_exists($frontendPath)) {
    echo "   ✓ File exists at: {$frontendPath}\n";

    // Read and show first 30 lines
    $content = file_get_contents($frontendPath);
    $lines = explode("\n", $content);
    $preview = array_slice($lines, 0, 30);

    echo "\n4. Preview (first 30 lines):\n";
    echo "   " . str_repeat("-", 70) . "\n";
    foreach ($preview as $i => $line) {
        $lineNum = $i + 1;
        echo sprintf("   %3d | %s\n", $lineNum, $line);
    }
    echo "   " . str_repeat("-", 70) . "\n";
} else {
    echo "   ✗ File not found\n";
    exit(1);
}

echo "\n✅ All tests passed!\n";
echo "\nNow when you save SEO settings in the admin panel,\n";
echo "the frontend index.html will be automatically regenerated.\n";
