const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"cashier.payment":{"uri":"stripe\/payment\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"cashier.webhook":{"uri":"stripe\/webhook","methods":["POST"]},"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"feedback.submit":{"uri":"feedback","methods":["POST"]},"public.track":{"uri":"api\/public\/track","methods":["GET","HEAD"]},"public.seo-settings":{"uri":"api\/public\/seo-settings","methods":["GET","HEAD"]},"robots.txt":{"uri":"robots.txt","methods":["GET","HEAD"]},"sitemap.xml":{"uri":"sitemap.xml","methods":["GET","HEAD"]},"home":{"uri":"\/","methods":["GET","HEAD"]},"products-list":{"uri":"products","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["POST"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"profile.language":{"uri":"profile\/language","methods":["PATCH"]},"profile.photo":{"uri":"profile-photos\/{userId}\/{filename}","methods":["GET","HEAD"],"parameters":["userId","filename"]},"languages.index":{"uri":"languages","methods":["GET","HEAD"]},"organizations.index":{"uri":"organizations","methods":["GET","HEAD"]},"organizations.create":{"uri":"organizations\/create","methods":["GET","HEAD"]},"organizations.store":{"uri":"organizations","methods":["POST"]},"organizations.show":{"uri":"organizations\/{organization}","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.edit":{"uri":"organizations\/{organization}\/edit","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.update":{"uri":"organizations\/{organization}","methods":["PUT","PATCH"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.destroy":{"uri":"organizations\/{organization}","methods":["DELETE"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.invites.accept":{"uri":"organizations\/{organization}\/invites\/{invite}\/accept","methods":["POST"],"parameters":["organization","invite"],"bindings":{"organization":"id","invite":"code"}},"organizations.invites.show":{"uri":"organizations\/{organization}\/invites\/{invite}","methods":["GET","HEAD"],"parameters":["organization","invite"],"bindings":{"organization":null,"invite":"code"}},"subscriptions.new":{"uri":"subscriptions\/new","methods":["GET","HEAD"]},"subscriptions.startup":{"uri":"subscriptions\/startup","methods":["GET","HEAD"]},"subscriptions.manage":{"uri":"subscriptions\/manage","methods":["GET","HEAD"]},"accounting.rate-types.index":{"uri":"accounting\/rate-types","methods":["GET","HEAD"]},"timezones.search":{"uri":"timezones\/search","methods":["GET","HEAD"]},"timezones.zipcode":{"uri":"timezones\/zipcode","methods":["GET","HEAD"]},"organizations.invites.index":{"uri":"organizations\/{organization}\/invites","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":null}},"organizations.invites.create":{"uri":"organizations\/{organization}\/invites\/create","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":null}},"organizations.invites.store":{"uri":"organizations\/{organization}\/invites","methods":["POST"],"parameters":["organization"],"bindings":{"organization":null}},"organizations.invites.edit":{"uri":"organizations\/{organization}\/invites\/{invite}\/edit","methods":["GET","HEAD"],"parameters":["organization","invite"],"bindings":{"organization":null,"invite":"code"}},"organizations.invites.update":{"uri":"organizations\/{organization}\/invites\/{invite}","methods":["PUT","PATCH"],"parameters":["organization","invite"],"bindings":{"organization":null,"invite":"code"}},"organizations.invites.destroy":{"uri":"organizations\/{organization}\/invites\/{invite}","methods":["DELETE"],"parameters":["organization","invite"],"bindings":{"organization":null,"invite":"code"}},"organizations.users.destroy":{"uri":"organizations\/{organization}\/users\/{user}","methods":["DELETE"],"parameters":["organization","user"],"bindings":{"organization":"id","user":"id"}},"organizations.users.transfer":{"uri":"organizations\/{organization}\/users\/{user}\/transfer","methods":["POST"],"parameters":["organization","user"],"bindings":{"organization":"id","user":"id"}},"organizations.invites.resend":{"uri":"organizations\/{organization}\/invites\/{invite}\/resend","methods":["POST"],"parameters":["organization","invite"],"bindings":{"organization":"id","invite":"code"}},"organizations.switch":{"uri":"organizations\/{organization}\/switch","methods":["POST"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.users":{"uri":"organizations\/{organization}\/users","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.roles":{"uri":"organizations\/{organization}\/roles","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.settings":{"uri":"organizations\/{organization}\/settings","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.settings.update":{"uri":"organizations\/{organization}\/settings","methods":["PUT"],"parameters":["organization"]},"organizations.billing":{"uri":"organizations\/{organization}\/billing","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.billing.update-seats":{"uri":"organizations\/{organization}\/billing\/update-seats","methods":["PUT"],"parameters":["organization"]},"organizations.document-templates-page":{"uri":"organizations\/{organization}\/document-templates-page","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.integration-settings":{"uri":"organizations\/{organization}\/integration-settings","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.integration-settings.store":{"uri":"organizations\/{organization}\/integration-settings","methods":["POST"],"parameters":["organization"]},"organizations.integration-settings.destroy":{"uri":"organizations\/{organization}\/integration-settings\/{setting}","methods":["DELETE"],"parameters":["organization","setting"]},"organizations.tpbank-settings":{"uri":"organizations\/{organization}\/tpbank-settings","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.tpbank-settings.store":{"uri":"organizations\/{organization}\/tpbank-settings","methods":["POST"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.tpbank-settings.destroy":{"uri":"organizations\/{organization}\/tpbank-settings","methods":["DELETE"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.seo-settings":{"uri":"organizations\/{organization}\/seo-settings","methods":["GET","HEAD"],"parameters":["organization"]},"organizations.seo-settings.update":{"uri":"organizations\/{organization}\/seo-settings","methods":["PUT"],"parameters":["organization"]},"organizations.seo-settings.html-preview":{"uri":"organizations\/{organization}\/seo-settings\/html-preview","methods":["GET","HEAD"],"parameters":["organization"]},"organizations.seo-settings.regenerate":{"uri":"organizations\/{organization}\/seo-settings\/regenerate","methods":["POST"],"parameters":["organization"]},"organizations.document-templates.index":{"uri":"organizations\/{organization}\/document-templates","methods":["GET","HEAD"],"parameters":["organization"]},"organizations.document-templates.show":{"uri":"organizations\/{organization}\/document-templates\/{documentTemplate}","methods":["GET","HEAD"],"parameters":["organization","documentTemplate"]},"organizations.document-templates.store":{"uri":"organizations\/{organization}\/document-templates","methods":["POST"],"parameters":["organization"]},"document-templates.validate":{"uri":"document-templates\/validate","methods":["POST"]},"document-templates.default":{"uri":"document-templates\/default\/{templateType}","methods":["GET","HEAD"],"parameters":["templateType"]},"organizations.permissions.role.store":{"uri":"organizations\/{organization}\/permissions\/role","methods":["POST"],"parameters":["organization"],"bindings":{"organization":"id"}},"organizations.permissions.role.destroy":{"uri":"organizations\/{organization}\/permissions\/role\/{role}","methods":["DELETE"],"parameters":["organization","role"],"bindings":{"organization":"id","role":"id"}},"organizations.permissions.role.update":{"uri":"organizations\/{organization}\/permissions\/role\/{role}","methods":["PATCH"],"parameters":["organization","role"],"bindings":{"organization":"id","role":"id"}},"facilities.search":{"uri":"facilities\/search","methods":["GET","HEAD"]},"facilities.index":{"uri":"facilities","methods":["GET","HEAD"]},"facilities.create":{"uri":"facilities\/create","methods":["GET","HEAD"]},"facilities.show":{"uri":"facilities\/{facility}","methods":["GET","HEAD"],"parameters":["facility"],"bindings":{"facility":"id"}},"facilities.edit":{"uri":"facilities\/{facility}\/edit","methods":["GET","HEAD"],"parameters":["facility"]},"facilities.destroy":{"uri":"facilities\/{facility}","methods":["DELETE"],"parameters":["facility"]},"facilities.store":{"uri":"facilities","methods":["POST"]},"facilities.update":{"uri":"facilities\/{facility}","methods":["PUT"],"parameters":["facility"]},"facilities.audit-history":{"uri":"facilities\/{facility}\/audit-history","methods":["GET","HEAD"],"parameters":["facility"]},"carriers.search":{"uri":"carriers\/search","methods":["GET","HEAD"]},"carriers.index":{"uri":"carriers","methods":["GET","HEAD"]},"carriers.create":{"uri":"carriers\/create","methods":["GET","HEAD"]},"carriers.show":{"uri":"carriers\/{carrier}","methods":["GET","HEAD"],"parameters":["carrier"],"bindings":{"carrier":"id"}},"carriers.edit":{"uri":"carriers\/{carrier}\/edit","methods":["GET","HEAD"],"parameters":["carrier"],"bindings":{"carrier":"id"}},"carriers.destroy":{"uri":"carriers\/{carrier}","methods":["DELETE"],"parameters":["carrier"],"bindings":{"carrier":"id"}},"carriers.update":{"uri":"carriers\/{carrier}","methods":["PUT"],"parameters":["carrier"]},"carriers.store":{"uri":"carriers","methods":["POST"]},"carriers.bounced-loads":{"uri":"carriers\/{carrier}\/bounced-loads","methods":["GET","HEAD"],"parameters":["carrier"],"bindings":{"carrier":"id"}},"carriers.audit-history":{"uri":"carriers\/{carrier}\/audit-history","methods":["GET","HEAD"],"parameters":["carrier"]},"carriers.refresh-safer":{"uri":"carriers\/{carrier}\/refresh-safer","methods":["POST"],"parameters":["carrier"]},"customers.audit-history":{"uri":"customers\/{customer}\/audit-history","methods":["GET","HEAD"],"parameters":["customer"]},"api.customers.show":{"uri":"api\/customers\/{customer}","methods":["GET","HEAD"],"parameters":["customer"]},"audit.linked-data":{"uri":"audit\/linked-data\/{type}\/{id}","methods":["GET","HEAD"],"parameters":["type","id"]},"carriers.fmcsa.lookup.name":{"uri":"carriers\/fmcsa\/name","methods":["GET","HEAD"]},"carriers.fmcsa.store":{"uri":"carriers\/fmcsa\/{carrierSaferReport}\/create","methods":["POST"],"parameters":["carrierSaferReport"]},"carriers.fmcsa.lookup.dot":{"uri":"carriers\/fmcsa\/dot","methods":["GET","HEAD"]},"customers.search":{"uri":"customers\/search","methods":["GET","HEAD"]},"customers.index":{"uri":"customers","methods":["GET","HEAD"]},"customers.create":{"uri":"customers\/create","methods":["GET","HEAD"]},"customers.show":{"uri":"customers\/{customer}","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.edit":{"uri":"customers\/{customer}\/edit","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.destroy":{"uri":"customers\/{customer}","methods":["DELETE"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.store":{"uri":"customers","methods":["POST"]},"customers.update":{"uri":"customers\/{customer}","methods":["PUT"],"parameters":["customer"]},"customers.facilities.store":{"uri":"customers\/{customer}\/facilities","methods":["POST"],"parameters":["customer"]},"customers.facilities.index":{"uri":"customers\/{customer}\/facilities","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.facilities.destroy":{"uri":"customers\/{customer}\/facilities\/{facility}","methods":["DELETE"],"parameters":["customer","facility"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"dashboard.cards.recent-shipments":{"uri":"dashboard\/cards\/recent-shipments","methods":["GET","HEAD"]},"dashboard.cards.recent-carriers":{"uri":"dashboard\/cards\/recent-carriers","methods":["GET","HEAD"]},"documents.store":{"uri":"documents","methods":["POST"]},"documents.update":{"uri":"documents\/{document}","methods":["PUT"],"parameters":["document"]},"documents.destroy":{"uri":"documents\/{document}","methods":["DELETE"],"parameters":["document"]},"documents.show":{"uri":"documents\/{document}","methods":["GET","HEAD"],"parameters":["document"]},"documents.index":{"uri":"documents\/{documentableType}\/{documentableId}","methods":["GET","HEAD"],"parameters":["documentableType","documentableId"]},"shipments.search":{"uri":"shipments\/search","methods":["GET","HEAD"]},"shipments.export":{"uri":"shipments\/export","methods":["GET","HEAD"]},"shipments.bulk.create":{"uri":"shipments\/bulk\/create","methods":["GET","HEAD"]},"invoices.bulk.generate":{"uri":"invoices\/bulk\/generate","methods":["GET","HEAD"]},"shipments.index":{"uri":"shipments","methods":["GET","HEAD"]},"shipments.create":{"uri":"shipments\/create","methods":["GET","HEAD"]},"shipments.show":{"uri":"shipments\/{shipment}","methods":["GET","HEAD"],"parameters":["shipment"],"bindings":{"shipment":"id"}},"shipments.edit":{"uri":"shipments\/{shipment}\/edit","methods":["GET","HEAD"],"parameters":["shipment"],"bindings":{"shipment":"id"}},"shipments.update":{"uri":"shipments\/{shipment}","methods":["PUT","PATCH"],"parameters":["shipment"],"bindings":{"shipment":"id"}},"shipments.destroy":{"uri":"shipments\/{shipment}","methods":["DELETE"],"parameters":["shipment"],"bindings":{"shipment":"id"}},"shipments.store":{"uri":"shipments","methods":["POST"]},"shipments.bulk.store":{"uri":"shipments\/bulk","methods":["POST"]},"shipments.updateShipmentNumber":{"uri":"shipments\/{shipment}\/shipment-number","methods":["PATCH"],"parameters":["shipment"]},"shipments.updateGeneral":{"uri":"shipments\/{shipment}\/general","methods":["PATCH"],"parameters":["shipment"]},"shipments.updateCarrierDetails":{"uri":"shipments\/{shipment}\/carrier-details","methods":["PATCH"],"parameters":["shipment"]},"shipments.updateCustomers":{"uri":"shipments\/{shipment}\/customers","methods":["PATCH"],"parameters":["shipment"]},"shipments.updateStops":{"uri":"shipments\/{shipment}\/stops","methods":["PATCH"],"parameters":["shipment"]},"shipments.updateState":{"uri":"shipments\/{shipment}\/state","methods":["PATCH"],"parameters":["shipment"]},"shipments.dispatch":{"uri":"shipments\/{shipment}\/dispatch","methods":["PATCH"],"parameters":["shipment"]},"shipments.cancel":{"uri":"shipments\/{shipment}\/cancel","methods":["PATCH"],"parameters":["shipment"]},"shipments.uncancel":{"uri":"shipments\/{shipment}\/uncancel","methods":["PATCH"],"parameters":["shipment"]},"shipments.bounce":{"uri":"shipments\/{shipment}\/bounce","methods":["POST"],"parameters":["shipment"]},"shipments.accounting":{"uri":"shipments\/{shipment}\/accounting","methods":["GET","HEAD"],"parameters":["shipment"]},"shipments.accounting.payables":{"uri":"shipments\/{shipment}\/accounting\/payables","methods":["POST"],"parameters":["shipment"]},"shipments.accounting.receivables":{"uri":"shipments\/{shipment}\/accounting\/receivables","methods":["POST"],"parameters":["shipment"]},"shipments.audit-history":{"uri":"shipments\/{shipment}\/audit-history","methods":["GET","HEAD"],"parameters":["shipment"]},"shipments.documents.generate-rate-confirmation":{"uri":"shipments\/{shipment}\/documents\/generate-rate-con","methods":["POST"],"parameters":["shipment"]},"shipments.documents.generate-customer-invoice":{"uri":"shipments\/{shipment}\/documents\/generate-customer-invoice\/{customer}","methods":["POST"],"parameters":["shipment","customer"]},"customers.documents.generate-bulk-invoice":{"uri":"customers\/{customer}\/documents\/generate-bulk-invoice","methods":["POST"],"parameters":["customer"]},"invoices.index":{"uri":"invoices","methods":["GET","HEAD"]},"invoices.create":{"uri":"invoices\/create","methods":["GET","HEAD"]},"invoices.store":{"uri":"invoices","methods":["POST"]},"invoices.show":{"uri":"invoices\/{invoice}","methods":["GET","HEAD"],"parameters":["invoice"],"bindings":{"invoice":"id"}},"invoices.edit":{"uri":"invoices\/{invoice}\/edit","methods":["GET","HEAD"],"parameters":["invoice"],"bindings":{"invoice":"id"}},"invoices.update":{"uri":"invoices\/{invoice}","methods":["PATCH"],"parameters":["invoice"]},"invoices.destroy":{"uri":"invoices\/{invoice}","methods":["DELETE"],"parameters":["invoice"]},"invoices.pdf":{"uri":"invoices\/{invoice}\/pdf","methods":["GET","HEAD"],"parameters":["invoice"]},"invoices.export":{"uri":"invoices-export","methods":["GET","HEAD"]},"invoices.check-payment":{"uri":"invoices\/{invoice}\/check-payment","methods":["POST"],"parameters":["invoice"]},"invoices.manual-payment":{"uri":"invoices\/{invoice}\/manual-payment","methods":["POST"],"parameters":["invoice"]},"invoices.refunded":{"uri":"invoices\/{invoice}\/refunded","methods":["POST"],"parameters":["invoice"]},"purchase-orders.index":{"uri":"purchase-orders","methods":["GET","HEAD"]},"purchase-orders.create":{"uri":"purchase-orders\/create","methods":["GET","HEAD"]},"purchase-orders.store":{"uri":"purchase-orders","methods":["POST"]},"purchase-orders.show":{"uri":"purchase-orders\/{purchaseOrder}","methods":["GET","HEAD"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.edit":{"uri":"purchase-orders\/{purchaseOrder}\/edit","methods":["GET","HEAD"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.update":{"uri":"purchase-orders\/{purchaseOrder}","methods":["PATCH"],"parameters":["purchaseOrder"]},"purchase-orders.destroy":{"uri":"purchase-orders\/{purchaseOrder}","methods":["DELETE"],"parameters":["purchaseOrder"]},"purchase-orders.unpaid-by-customer":{"uri":"purchase-orders\/unpaid-by-customer\/{customerId}","methods":["GET","HEAD"],"parameters":["customerId"]},"purchase-orders.export":{"uri":"purchase-orders-export","methods":["GET","HEAD"]},"purchase-orders.add-tracking":{"uri":"purchase-orders\/{purchaseOrder}\/tracking","methods":["POST"],"parameters":["purchaseOrder"]},"purchase-orders.check-payment":{"uri":"purchase-orders\/{purchaseOrder}\/check-payment","methods":["POST"],"parameters":["purchaseOrder"]},"purchase-orders.manual-payment":{"uri":"purchase-orders\/{purchaseOrder}\/manual-payment","methods":["POST"],"parameters":["purchaseOrder"]},"purchase-orders.cancel":{"uri":"purchase-orders\/{purchaseOrder}\/cancel","methods":["POST"],"parameters":["purchaseOrder"]},"purchase-order-items.update":{"uri":"purchase-order-items\/{purchaseOrderItem}","methods":["PATCH"],"parameters":["purchaseOrderItem"]},"exchange-rates.index":{"uri":"exchange-rates","methods":["GET","HEAD"]},"exchange-rates.sync":{"uri":"exchange-rates\/sync","methods":["POST"]},"exchange-rates.update":{"uri":"exchange-rates\/{setting}","methods":["PATCH"],"parameters":["setting"]},"bank-statement.index":{"uri":"bank-statement","methods":["GET","HEAD"]},"tpbank.login":{"uri":"api\/tpbank\/login","methods":["POST"]},"tpbank.transaction-history":{"uri":"api\/tpbank\/transaction-history","methods":["GET","HEAD"]},"tpbank.check-token":{"uri":"api\/tpbank\/check-token","methods":["GET","HEAD"]},"tpbank.logout":{"uri":"api\/tpbank\/logout","methods":["POST"]},"tpbank.export":{"uri":"api\/tpbank\/export","methods":["POST"]},"bounce-reasons":{"uri":"bounce-reasons","methods":["GET","HEAD"]},"notes.destroy":{"uri":"notes\/{note}","methods":["DELETE"],"parameters":["note"]},"notes.index":{"uri":"notes\/{notableType}\/{notableId}","methods":["GET","HEAD"],"parameters":["notableType","notableId"]},"notes.store":{"uri":"notes\/{notableType}\/{notableId}","methods":["POST"],"parameters":["notableType","notableId"]},"shipments.check-calls.index":{"uri":"shipments\/{shipment}\/check-calls","methods":["GET","HEAD"],"parameters":["shipment"],"bindings":{"shipment":"id"}},"shipments.check-calls.destroy":{"uri":"shipments\/{shipment}\/check-calls\/{checkcall}","methods":["DELETE"],"parameters":["shipment","checkcall"],"bindings":{"shipment":"id","checkcall":"id"}},"shipments.check-calls.store":{"uri":"shipments\/{shipment}\/check-calls","methods":["POST"],"parameters":["shipment"]},"locations.search":{"uri":"locations\/search","methods":["GET","HEAD"]},"locations.store":{"uri":"locations","methods":["POST"]},"contacts.search":{"uri":"contacts\/search","methods":["GET","HEAD"]},"contacts.store":{"uri":"contacts","methods":["POST"]},"contacts.update":{"uri":"contacts\/{contact}","methods":["PUT"],"parameters":["contact"]},"contacts.destroy":{"uri":"contacts\/{contact}","methods":["DELETE"],"parameters":["contact"]},"contacts.types":{"uri":"contacts\/types\/{contactable}","methods":["GET","HEAD"],"parameters":["contactable"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
