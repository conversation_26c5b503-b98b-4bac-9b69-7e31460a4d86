<?php

namespace App\Actions\SeoSettings;

use App\Enums\Permission;
use App\Models\Organizations\SeoSetting;
use Exception;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class RegenerateFrontendHtml
{
    use AsAction;

    /**
     * Manually trigger regeneration of frontend HTML
     */
    public function handle(): array
    {
        $organization = current_organization();
        if (!$organization) {
            throw new Exception("A current organization is required to regenerate frontend HTML");
        }

        $seoSetting = SeoSetting::where('organization_id', $organization->id)->first();

        $generator = new GenerateSeoHtml();
        $result = $generator->execute($seoSetting);

        if ($result) {
            Log::info('Frontend HTML manually regenerated by admin', [
                'organization_id' => $organization->id,
            ]);

            return [
                'success' => true,
                'message' => 'Frontend HTML regenerated successfully',
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to regenerate frontend HTML',
        ];
    }

    public function asController(ActionRequest $request)
    {
        $result = $this->handle();

        if ($result['success']) {
            return redirect()->back()->with('success', $result['message']);
        }

        return redirect()->back()->with('error', $result['message']);
    }

    public function authorize(ActionRequest $request): bool
    {
        return Gate::allows('update', SeoSetting::class);
    }
}
