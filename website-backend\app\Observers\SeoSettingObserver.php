<?php

namespace App\Observers;

use App\Actions\SeoSettings\GenerateSeoHtml;
use App\Models\Organizations\SeoSetting;
use Illuminate\Support\Facades\Log;

class SeoSettingObserver
{
    /**
     * Handle the SeoSetting "saved" event.
     *
     * Automatically regenerate frontend index.html with new SEO tags
     */
    public function saved(SeoSetting $seoSetting): void
    {
        try {
            Log::info('SEO settings saved, regenerating frontend HTML...', [
                'seo_setting_id' => $seoSetting->id,
            ]);

            $generator = new GenerateSeoHtml();
            $result = $generator->execute($seoSetting);

            if ($result) {
                Log::info('Frontend HTML regenerated successfully after SEO save');
            } else {
                Log::error('Failed to regenerate frontend HTML after SEO save');
            }
        } catch (\Exception $e) {
            Log::error('Error in SeoSettingObserver: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
        }
    }

    /**
     * Handle the SeoSetting "deleted" event.
     *
     * Regenerate with default values if SEO settings are deleted
     */
    public function deleted(SeoSetting $seoSetting): void
    {
        try {
            Log::info('SEO settings deleted, regenerating frontend HTML with defaults...');

            $generator = new GenerateSeoHtml();
            $result = $generator->execute(null); // Use defaults

            if ($result) {
                Log::info('Frontend HTML regenerated with defaults after SEO deletion');
            }
        } catch (\Exception $e) {
            Log::error('Error regenerating HTML after SEO deletion: ' . $e->getMessage());
        }
    }
}
